// ui/PokemonCaughtScreen.js
// Screen to display caught Pokemon and player's team

import { Component } from './Component.js';
import { logger } from '../utils/logger.js';
import { loadTeam, addToTeam, removeFromTeam, makePokemonBuddy } from '../storage/teamStorage.js';
import { pokemonManager } from '../services/pokemon-manager.js';
import { getGermanPokemonName } from '../utils/pokemon-display-names.js';
import { registerBackButtonHandler } from '../capacitor/app.js';
import { gameState } from '../state/game-state.js';

export class PokemonCaughtScreen extends Component {
  constructor(container, options = {}) {
    super(container, options);
    this.caughtPokemon = options.caughtPokemon || [];
    this.teamPokemon = [];
    this.starterPokemon = null;
    this.pokedexData = [];
    this.isLoading = true;
    this.isDestroyed = false;
    this.loadData();
  }

  /**
   * Load necessary data for the screen
   */
  async loadData() {
    try {
      // Load starter Pokemon data
      const starterResp = await fetch('./starter-pokemon.json');
      this.starterPokemon = await starterResp.json();

      // Check if destroyed before continuing
      if (this.isDestroyed) return;

      // Check if we have updated starter Pokemon data in localStorage
      const storedStarterPokemon = localStorage.getItem('starterPokemon');
      if (storedStarterPokemon) {
        try {
          const parsedData = JSON.parse(storedStarterPokemon);
          // Merge the stored data with the loaded data
          this.starterPokemon = { ...this.starterPokemon, ...parsedData };
          logger.debug(`Loaded starter Pokemon from localStorage: Level ${this.starterPokemon.level}, XP: ${this.starterPokemon.experience}`);
        } catch (e) {
          logger.error('Error parsing stored starter Pokemon:', e);
        }
      }

      // Check if destroyed before continuing
      if (this.isDestroyed) return;

      // Load pokedex data - primarily from gameState, fallback to loading if needed
      if (gameState.pokedexData && gameState.pokedexData.length > 0) {
        // Use already loaded data from gameState
        this.pokedexData = gameState.pokedexData;
        logger.debug('Using pokedex data from gameState');
      } else {
        // If gameState doesn't have data yet, load it
        logger.debug('gameState.pokedexData not available, loading via gameState.loadPokedexData()');
        await gameState.loadPokedexData();
        this.pokedexData = gameState.pokedexData;

        // If still no data, use PokedexDataManager as final fallback
        if (!this.pokedexData || this.pokedexData.length === 0) {
          logger.warn('gameState.loadPokedexData() did not populate data, using PokedexDataManager fallback');
          const { PokedexDataManager } = await import('../utils/pokedex-data-manager.js');
          this.pokedexData = await PokedexDataManager.getPokedexData();
        }
      }

      // Initialize the Pokemon manager
      await pokemonManager.initialize();

      // Check if destroyed before continuing
      if (this.isDestroyed) return;

      // Load caught Pokemon directly from the Pokemon manager
      this.caughtPokemon = pokemonManager.getCaughtPokemon();
      logger.debug(`Loaded ${this.caughtPokemon.length} caught Pokemon from Pokemon manager`);

      // Log dex_number for each caught Pokemon
      logger.debug('===== CAUGHT POKEMON DEX NUMBERS =====');
      this.caughtPokemon.forEach((pokemon, index) => {
        logger.debug(`Caught Pokemon ${index + 1}: ${pokemon.name} (ID: ${pokemon.id}) has dex_number: ${pokemon.dex_number}`);
      });
      logger.debug('===================================');

      // Load team Pokemon
      this.teamPokemon = await loadTeam();
      logger.debug(`Loaded ${this.teamPokemon.length} team Pokemon`);

      // Check if destroyed before continuing
      if (this.isDestroyed) return;

      // Add starter Pokemon to team if team is empty
      if (this.teamPokemon.length === 0 && this.starterPokemon) {
        // Create a Pokemon object for Pikachu
        const pikachuData = this.findPokemonByName('Pikachu');
        if (pikachuData) {
          const pikachu = {
            id: 'starter-pikachu',
            name: pikachuData.de || 'Pikachu',
            level: this.starterPokemon.level || 5,
            experience: this.starterPokemon.experience || 125,
            types: pikachuData.types || ['electric'],
            image: pikachuData.image_url || './src/PokemonSprites/25.png',
            rarity: 'starter',
            isStarter: true
          };

          // Add to team
          const result = await addToTeam(pikachu);
          if (result.success) {
            this.teamPokemon = await loadTeam();
            logger.debug('Added starter Pikachu to team');
          }
        }
      }

      // Log active team information
      this.logActiveTeam();

      this.isLoading = false;

      // Render the screen after data is loaded
      this.render();
      this.addEventListeners();
    } catch (e) {
      logger.error('Error loading Pokemon team data:', e);
      this.isLoading = false;
      this.render();
      this.addEventListeners();
    }
  }

  /**
   * Find Pokemon data in pokedex by name
   * @param {string} name - Pokemon name
   * @returns {Object|null} - Pokemon data or null if not found
   */
  findPokemonByName(name) {
    // Validate input
    if (!name || typeof name !== 'string' || name.trim().length === 0) {
      logger.warn(`Invalid Pokemon name provided to findPokemonByName: ${name}`);
      return null;
    }

    if (!this.pokedexData || !Array.isArray(this.pokedexData)) {
      logger.warn('Pokedex data is not available or invalid in findPokemonByName');
      return null;
    }

    const normalizedName = name.toLowerCase().trim();
    const result = this.pokedexData.find(p =>
      p?.name?.toLowerCase() === normalizedName ||
      (p?.de && p.de.toLowerCase() === normalizedName)
    );

    if (!result) {
      logger.debug(`Pokemon not found in pokedex: ${name}`);
    }

    return result;
  }

  /**
   * Render the Pokemon caught screen with team section
   * @returns {HTMLElement} - The rendered container
   */
  render() {
    if (this.isLoading) {
      return this.renderLoadingState();
    }

    try {
      return this.renderMainContent();
    } catch (e) {
      logger.error('Error rendering Pokemon caught screen:', e);
      return this.renderErrorState(e);
    }
  }

  /**
   * Render loading state
   * @returns {HTMLElement} - The rendered container
   */
  renderLoadingState() {
    this.container.innerHTML = `
      <div class="screen-header pokemon-caught-header">
        <button class="back-btn" id="pokemon-caught-back-btn" aria-label="Zurück">
          <img src="./icons/materialicons/chevronleft.svg" alt="Zurück" class="icon-svg" width="38" height="38" />
        </button>
        <h1>Pokémon Team</h1>
        <div class="header-right"></div>
      </div>
      <div class="pokemon-caught-loading">
        <p>Loading Pokemon data...</p>
      </div>
    `;

    this.elements.backButton = this.container.querySelector('#pokemon-caught-back-btn');
    return this.container;
  }

  /**
   * Render main content
   * @returns {HTMLElement} - The rendered container
   */
  renderMainContent() {
    // Validate required data
    if (!this.pokedexData || !Array.isArray(this.pokedexData)) {
      throw new Error('Pokedex data is not available or invalid');
    }

    this.container.innerHTML = `
      <div class="screen-header pokemon-caught-header">
        <button class="back-btn" id="pokemon-caught-back-btn" aria-label="Zurück">
          <img src="./icons/materialicons/chevronleft.svg" alt="Zurück" class="icon-svg" width="38" height="38" />
        </button>
        <h1>Pokémon Team</h1>
        <div class="header-right"></div>
      </div>

      <div class="pokemon-team-section">
        <h2>Team (${this.teamPokemon.length}/6)</h2>
        <div class="pokedex-grid">
          ${this.renderTeamCards()}
        </div>
      </div>

      <div class="pokemon-caught-section">
        <h2>Gefangene Pokémon</h2>
        <div class="pokedex-grid">
          ${this.renderCaughtPokemonCards()}
        </div>
      </div>
    `;

    // Store elements for event handling
    this.elements.backButton = this.container.querySelector('#pokemon-caught-back-btn');

    // Adjust font sizes after DOM is ready
    requestAnimationFrame(() => {
      this.adjustCardNameFontSize();
    });

    this.isRendered = true;
    return this.container;
  }

  /**
   * Render error state
   * @param {Error} error - The error that occurred
   * @returns {HTMLElement} - The rendered container
   */
  renderErrorState(error) {
    const errorMessage = error?.message || 'Unknown error occurred';

    this.container.innerHTML = `
      <div class="screen-header pokemon-caught-header">
        <button class="back-btn" id="pokemon-caught-back-btn" aria-label="Zurück">
          <img src="./icons/materialicons/chevronleft.svg" alt="Zurück" class="icon-svg" width="38" height="38" />
        </button>
        <h1>Pokémon Team</h1>
        <div class="header-right"></div>
      </div>
      <div class="pokemon-caught-error">
        <h2>Error Loading Data</h2>
        <p>An error occurred while loading your Pokemon data:</p>
        <p class="error-details">${errorMessage}</p>
        <button class="retry-btn" onclick="location.reload()">Retry</button>
        <button class="back-btn-error" onclick="history.back()">Go Back</button>
      </div>
    `;

    this.elements.backButton = this.container.querySelector('#pokemon-caught-back-btn');

    this.isRendered = true;
    return this.container;
  }

  /**
   * Adjust font size of Pokemon card names to prevent overflow
   */
  adjustCardNameFontSize() {
    // Check if container exists and is mounted
    if (!this.container || !this.container.isConnected) {
      return;
    }

    // Select all Pokemon card name elements
    const nameElements = this.container.querySelectorAll('.pokedex-card-name');

    // If no elements found, exit safely
    if (nameElements.length === 0) {
      return;
    }

    nameElements.forEach(element => {
      // Reset to initial font size (1rem = 16px)
      let currentFontSizePx = 16;
      const minFontSizePx = 10.4; // 0.65rem * 16px
      const stepSizePx = 3.2; // 0.2rem * 16px

      // Set initial font size
      element.style.fontSize = `${currentFontSizePx}px`;

      // Check if text overflows and reduce font size if needed
      while (element.scrollWidth > element.clientWidth && currentFontSizePx >= minFontSizePx) {
        currentFontSizePx -= stepSizePx;
        element.style.fontSize = `${currentFontSizePx}px`;
      }
    });
  }

  /**
   * Render the team cards (up to 6 Pokémon)
   * @returns {string} - HTML for team cards
   */
  renderTeamCards() {
    const cards = [];
    const maxTeamSize = 6;

    // Render team Pokémon
    if (this.teamPokemon && this.teamPokemon.length > 0) {
      this.teamPokemon.forEach((pokemon, index) => {
        // Find additional data from pokedex
        const pokedexEntry = this.pokedexData.find(p =>
          p.name === pokemon.name ||
          (p.de && p.de === pokemon.name) ||
          (pokemon.pokedexId && p.id === pokemon.pokedexId)
        ) || {};

        // Get Pokémon types
        const types = pokemon.types || pokedexEntry.types || ['Normal'];
        const [type1, type2] = types;

        // Determine card background
        let cardClass = type2 ? '' : `type-bg-${type1.toLowerCase()}`;
        let cardStyle = '';

        if (type2) {
          cardStyle = `background: linear-gradient(135deg, var(--type-${type1.toLowerCase()}) 60%, var(--type-${type2.toLowerCase()}) 80%);`;
        }

        // Get Pokémon name and image
        const displayName = getGermanPokemonName(pokemon);
        const dexNumber = pokemon.dex_number || pokedexEntry.dex_number || '0';
        const image = pokemon.image || pokedexEntry.image_url || `./src/PokemonSprites/${dexNumber}.png`;
        const level = pokemon.level || '?';
        const experience = pokemon.experience || 0;
        const nextLevel = level + 1;

        // Log dex_number for debugging
        logger.debug(`Rendering team card for ${displayName}: pokemon.dex_number=${pokemon.dex_number}, pokedexEntry.dex_number=${pokedexEntry.dex_number}, final dexNumber=${dexNumber}`);

        // Generate a unique ID for this Pokémon element
        const pokemonId = `team-pokemon-${pokemon.id}`;
        const cardContainerId = `team-card-container-${pokemon.id}`;

        // Check if this is the buddy (first Pokémon)
        const isBuddy = index === 0;
        const buddyClass = isBuddy ? 'buddy-pokemon' : '';

        // Calculate experience progress
        (async () => {
          try {
            if (typeof level === 'number') {
              // Import the experience system
              const { getExpForLevel, getExpCurveForRarity, getLevelForExp } = await import('../services/experience-system.js');

              // Get the experience curve
              const curve = getExpCurveForRarity(pokemon.rarity || 'common');

              // Calculate experience thresholds
              const currentLevelExp = getExpForLevel(level, curve);
              const nextLevelExp = getExpForLevel(nextLevel, curve);

              // Ensure experience is a number
              const totalExp = typeof experience === 'number' ? experience : currentLevelExp;

              // Calculate what level the Pokemon should be based on its experience
              const calculatedLevel = getLevelForExp(totalExp, curve);

              // Log if there's a mismatch between stored level and calculated level
              if (calculatedLevel !== level) {
                logger.debug(`Level-XP mismatch for ${pokemon.name}: XP ${totalExp} corresponds to level ${calculatedLevel}, but stored level is ${level}`);
              }

              // Calculate progress
              let expInCurrentLevel = totalExp - currentLevelExp;
              const expNeededForNextLevel = nextLevelExp - currentLevelExp;

              // Fix for negative XP values - ensure we have at least 0 XP in current level
              if (expInCurrentLevel < 0) {
                logger.debug(`Fixing negative XP for ${pokemon.name}: ${expInCurrentLevel} -> 0`);
                expInCurrentLevel = 0;
              }

              const progressPercentage = Math.min(100, Math.floor((expInCurrentLevel / expNeededForNextLevel) * 100));

              // Update the experience bar
              const expBar = document.querySelector(`#${pokemonId} .pokemon-exp-fill`);
              if (expBar) {
                expBar.style.width = `${progressPercentage}%`;
              }

              // Update the experience text
              const expText = document.querySelector(`#${pokemonId} .pokemon-exp-text`);
              if (expText) {
                expText.textContent = `${expInCurrentLevel}/${expNeededForNextLevel} XP (${progressPercentage}%)`;
              }

              // Log the XP details for debugging
              logger.debug(`XP details for ${pokemon.name} (Level ${level}): Total XP=${totalExp}, Current Level Base XP=${currentLevelExp}, XP in current level=${expInCurrentLevel}, XP needed for next level=${expNeededForNextLevel}, Progress=${progressPercentage}%`);
            }
          } catch (e) {
            logger.error('Error calculating experience progress:', e);
          }
        })();

        // Render make buddy button only for non-buddy Pokémon
        const makeBuddyButton = !isBuddy ? `
          <button class="make-buddy-btn team-action-btn" data-pokemon-id="${pokemon.id}" aria-label="Als Buddy festlegen" title="Als Buddy festlegen">
            <img src="./icons/materialicons/buddy.svg" alt="Als Buddy festlegen" class="icon-svg" width="24" height="24" />
          </button>
        ` : '';

        cards.push(`
          <div id="${cardContainerId}" class="pokemon-card-container" data-pokemon-id="${pokemon.id}">
            ${isBuddy ? `<div class="buddy-label-container"><div class="buddy-label">Buddy</div></div>` : ''}
            <div class="pokemon-card-inner">
              <div id="${pokemonId}" class="pokemon-card-front pokedex-card ${cardClass} ${buddyClass}"${cardStyle ? ` style="${cardStyle}"` : ''}>
                <div class="pokedex-card-header">
                  <div class="pokedex-card-name" data-name="${displayName}">${displayName}</div>
                  <div class="pokedex-card-dex">${String(dexNumber).padStart(3, '0')}</div>
                </div>
                <div class="pokedex-card-types">
                  ${types.map(t => {
                    const typeKey = t.toLowerCase();
                    return `<span class="type-label type-${typeKey}">${t}</span>`;
                  }).join('')}
                </div>
                <div class="pokemon-exp-container">
                  <div class="pokemon-exp-bar">
                    <div class="pokemon-exp-fill" style="width: 0%"></div>
                  </div>
                  <div class="pokemon-exp-text">Loading XP...</div>
                </div>
                <img class="pokedex-card-img" src="${image}" alt="${displayName}" />
              </div>
              <div class="pokemon-card-back"${cardStyle ? ` style="${cardStyle}"` : ''}>
                <div class="card-back-content">
                  <button class="team-action-btn remove-from-team-btn" data-pokemon-id="${pokemon.id}" aria-label="Aus Team entfernen">
                    <img src="./icons/materialicons/logout.svg" alt="Aus Team entfernen" class="icon-svg" width="24" height="24" />
                  </button>
                  ${makeBuddyButton}
                </div>
              </div>
            </div>
          </div>
        `);
      });
    }

    // Add empty slots to fill up to 6
    const emptySlots = Math.max(0, maxTeamSize - (this.teamPokemon?.length || 0));
    for (let i = 0; i < emptySlots; i++) {
      cards.push(`
        <div class="pokedex-card empty-team-slot" style="background-color: var(--really-grey); opacity: 0.5;">
          <!-- Empty slot -->
        </div>
      `);
    }

    return cards.join('');
  }

  /**
   * Render caught Pokemon cards
   * @returns {string} - HTML for caught Pokemon cards
   */
  /**
 * Render caught Pokemon cards
 * @returns {string} - HTML for caught Pokemon cards
 */
renderCaughtPokemonCards() {
  if (!this.caughtPokemon || this.caughtPokemon.length === 0) {
    return `
      <div class="pokemon-caught-empty-message">
        <p>Noch keine Pokémon gefangen. Gewinne Kämpfe gegen wilde Pokémon, um sie zu fangen!</p>
      </div>
    `;
  }

  // Filter out Pokemon that are already in the team
  const teamPokemonIds = this.teamPokemon.map(p => p.id);
  const availablePokemon = this.caughtPokemon.filter(p => !teamPokemonIds.includes(p.id));

  if (availablePokemon.length === 0) {
    return `
      <div class="pokemon-caught-empty-message">
        <p>Alle gefangenen Pokémon sind bereits in deinem Team.</p>
      </div>
    `;
  }

  return availablePokemon.map(pokemon => {
    // Find additional data from pokedex using name instead of id
    const pokedexEntry = this.pokedexData.find(p =>
      p.name === pokemon.name ||
      (p.de && p.de === pokemon.name) ||
      (pokemon.pokedexId && p.id === pokemon.pokedexId)
    ) || {};

    // Get Pokemon types
    const types = pokemon.types || pokedexEntry.types || ['Normal'];
    const [type1, type2] = types;

    // Determine card background
    let cardClass = type2 ? '' : `type-bg-${type1.toLowerCase()}`;
    let cardStyle = '';

    if (type2) {
      cardStyle = `background: linear-gradient(135deg, var(--type-${type1.toLowerCase()}) 60%, var(--type-${type2.toLowerCase()}) 80%);`;
    }

    // Get Pokemon name and image
    const displayName = getGermanPokemonName(pokemon);
    const dexNumber = pokemon.dex_number || pokedexEntry.dex_number || '0';
    const image = pokemon.image || pokedexEntry.image_url || `./src/PokemonSprites/${dexNumber}.png`;
    const level = pokemon.level || '?';

    // Log dex_number for debugging
    logger.debug(`Rendering caught card for ${displayName}: pokemon.dex_number=${pokemon.dex_number}, pokedexEntry.dex_number=${pokedexEntry.dex_number}, final dexNumber=${dexNumber}`);

    // Generate unique IDs
    const pokemonId = `caught-pokemon-${pokemon.id}`;
    const cardContainerId = `caught-card-container-${pokemon.id}`;

    // Experience calculation for caught Pokemon
    const experience = pokemon.experience || 0;
    const normalizedLevel = Number.isFinite(pokemon.level) ? pokemon.level : Number(pokemon.level) || 1;
    const nextLevel = normalizedLevel + 1;

    // Asynchronous experience calculation (analog to team Pokemon implementation)
    (async () => {
      try {
        if (typeof normalizedLevel === 'number') {
          // Import the experience system
          const { getExpForLevel, getExpCurveForRarity, getLevelForExp } = await import('../services/experience-system.js');

          // Get the experience curve
          const curve = getExpCurveForRarity(pokemon.rarity || 'common');

          // Calculate experience thresholds
          const currentLevelExp = getExpForLevel(normalizedLevel, curve);
          const nextLevelExp = getExpForLevel(nextLevel, curve);

          // Ensure experience is a number
          const totalExp = typeof experience === 'number' ? experience : currentLevelExp;

          // Calculate what level the Pokemon should be based on its experience
          const calculatedLevel = getLevelForExp(totalExp, curve);

          // Log if there's a mismatch between stored level and calculated level
          if (calculatedLevel !== normalizedLevel) {
            logger.debug(`Level-XP mismatch for ${pokemon.name}: XP ${totalExp} corresponds to level ${calculatedLevel}, but stored level is ${normalizedLevel}`);
          }

          // Calculate progress
          let expInCurrentLevel = totalExp - currentLevelExp;
          const expNeededForNextLevel = nextLevelExp - currentLevelExp;

          // Fix for negative XP values - ensure we have at least 0 XP in current level
          if (expInCurrentLevel < 0) {
            logger.debug(`Fixing negative XP for ${pokemon.name}: ${expInCurrentLevel} -> 0`);
            expInCurrentLevel = 0;
          }

          const progressPercentage = Math.min(100, Math.floor((expInCurrentLevel / expNeededForNextLevel) * 100));

          // Update the experience bar and text after DOM insertion
          requestAnimationFrame(() => {
            // Update the experience bar
            const expBar = document.querySelector(`#${pokemonId} .pokemon-exp-fill`);
            if (expBar) {
              expBar.style.width = `${progressPercentage}%`;
            }

            // Update the experience text
            const expText = document.querySelector(`#${pokemonId} .pokemon-exp-text`);
            if (expText) {
              expText.textContent = `${expInCurrentLevel}/${expNeededForNextLevel} XP (${progressPercentage}%)`;
            }
          });

          // Log the XP details for debugging
          logger.debug(`XP details for ${pokemon.name} (Level ${normalizedLevel}): Total XP=${totalExp}, Current Level Base XP=${currentLevelExp}, XP in current level=${expInCurrentLevel}, XP needed for next level=${expNeededForNextLevel}, Progress=${progressPercentage}%`);
        }
      } catch (e) {
        logger.error('Error calculating experience progress:', e);
      }
    })();

    // Check if team is full
    const isTeamFull = this.teamPokemon.length >= 6;
    const addButtonClass = isTeamFull ? 'disabled' : '';
    const addButtonTitle = isTeamFull ? 'Team ist voll (max. 6 Pokémon)' : 'Zum Team hinzufügen';

    return `
      <div id="${cardContainerId}" class="pokemon-card-container" data-pokemon-id="${pokemon.id}">
        <div class="pokemon-card-inner">
          <div id="${pokemonId}" class="pokemon-card-front pokedex-card ${cardClass}"${cardStyle ? ` style="${cardStyle}"` : ''}>
            <div class="pokedex-card-header">
              <div class="pokedex-card-name" data-name="${displayName}">${displayName}</div>
              <div class="pokedex-card-dex">${String(dexNumber).padStart(3, '0')}</div>
            </div>
            <div class="pokedex-card-types">
              ${types.map(t => {
                const typeKey = t.toLowerCase();
                return `<span class="type-label type-${typeKey}">${t}</span>`;
              }).join('')}
            </div>
            <div class="pokemon-exp-container">
              <div class="pokemon-exp-bar">
                <div class="pokemon-exp-fill" style="width: 0%"></div>
              </div>
              <div class="pokemon-exp-text">Loading XP...</div>
            </div>
            <img class="pokedex-card-img" src="${image}" alt="${displayName}" />
          </div>
          <div class="pokemon-card-back"${cardStyle ? ` style="${cardStyle}"` : ''}>
            <div class="card-back-content">
              <button class="team-action-btn add-to-team-btn ${addButtonClass}"
                      data-pokemon-id="${pokemon.id}"
                      aria-label="${addButtonTitle}"
                      title="${addButtonTitle}"
                      ${isTeamFull ? 'disabled' : ''}>
                <img src="./icons/materialicons/login.svg" alt="Zum Team hinzufügen" class="icon-svg" width="24" height="24" />
              </button>
              <button class="team-action-btn release-pokemon-btn"
                      data-pokemon-id="${pokemon.id}"
                      aria-label="Pokemon freilassen"
                      title="Pokemon freilassen">
                <img src="./icons/materialicons/bye.svg" alt="Pokemon freilassen" class="icon-svg" width="24" height="24" />
              </button>
            </div>
          </div>
        </div>
      </div>
    `;
  }).join('');
}

  /**
   * Log information about the active team
   */
  async logActiveTeam() {
    try {
      // Import the experience system
      const { getExpForLevel, getExpCurveForRarity, getLevelForExp } = await import('../services/experience-system.js');

      // Log dex_number for each team Pokemon
      logger.debug('===== TEAM POKEMON DEX NUMBERS =====');
      this.teamPokemon.forEach((pokemon, index) => {
        logger.debug(`Team Pokemon ${index + 1}: ${pokemon.name} (ID: ${pokemon.id}) has dex_number: ${pokemon.dex_number}`);
      });
      logger.debug('===================================');

      // Create an array to hold team members with detailed information
      const teamDetails = [];

      // Process each team member
      for (const pokemon of this.teamPokemon) {
        try {
          // Get Pokemon's level and experience
          const level = pokemon.level || 1;
          const experience = typeof pokemon.experience === 'number' ? pokemon.experience : 0;
          const rarity = pokemon.rarity || 'common';

          // Get the experience curve
          const curve = getExpCurveForRarity(rarity);

          // Calculate what level the Pokemon should be based on its experience
          const calculatedLevel = getLevelForExp(experience, curve);

          // Log if there's a mismatch between stored level and calculated level
          if (calculatedLevel !== level) {
            logger.debug(`Level-XP mismatch for ${pokemon.name}: XP ${experience} corresponds to level ${calculatedLevel}, but stored level is ${level}`);
          }

          // Calculate experience thresholds
          const currentLevelExp = getExpForLevel(level, curve);
          const nextLevelExp = getExpForLevel(level + 1, curve);
          let expInCurrentLevel = experience - currentLevelExp;

          // Fix for negative XP values
          if (expInCurrentLevel < 0) {
            logger.debug(`Fixing negative XP in logActiveTeam for ${pokemon.name}: ${expInCurrentLevel} -> 0`);
            expInCurrentLevel = 0;
          }

          const expNeededForNextLevel = nextLevelExp - currentLevelExp;
          const progressPercentage = Math.min(100, Math.floor((expInCurrentLevel / expNeededForNextLevel) * 100));

          // Add Pokemon to the team details
          teamDetails.push({
            id: pokemon.id,
            name: pokemon.name,
            level,
            calculatedLevel,
            rarity,
            curve,
            experience,
            currentLevelExp,
            nextLevelExp,
            expInCurrentLevel,
            expNeededForNextLevel,
            progressPercentage,
            isStarter: pokemon.isStarter || false
          });
        } catch (e) {
          logger.error(`Error processing team member ${pokemon.name}:`, e);
        }
      }

      // Log team information
      logger.debug('===== ACTIVE TEAM INFORMATION =====');
      logger.debug(`Team size: ${this.teamPokemon.length}/6 Pokemon`);

      // Log detailed information for each team member
      teamDetails.forEach((pokemon, index) => {
        logger.debug(`\n[TEAM MEMBER ${index + 1}]`);
        logger.debug(`Name: ${pokemon.name}${pokemon.isStarter ? ' (Starter)' : ''}`);
        logger.debug(`ID: ${pokemon.id}`);
        logger.debug(`Level: ${pokemon.level}${pokemon.calculatedLevel !== pokemon.level ? ` (Calculated: ${pokemon.calculatedLevel})` : ''}`);
        logger.debug(`Rarity: ${pokemon.rarity}`);
        logger.debug(`Experience Curve: ${pokemon.curve}`);
        logger.debug(`\nEXPERIENCE DETAILS:`);
        logger.debug(`Total XP: ${pokemon.experience}`);
        logger.debug(`Base XP for Level ${pokemon.level}: ${pokemon.currentLevelExp}`);
        logger.debug(`XP for Level ${pokemon.level + 1}: ${pokemon.nextLevelExp}`);
        logger.debug(`XP in current level: ${pokemon.expInCurrentLevel}/${pokemon.expNeededForNextLevel} (${pokemon.progressPercentage}%)`);
        logger.debug(`XP needed for next level: ${pokemon.expNeededForNextLevel}`);
      });

      if (teamDetails.length === 0) {
        logger.debug('No Pokemon in team');
      }

      logger.debug('===================================');
    } catch (e) {
      logger.error('Error logging team information:', e);
    }
  }

  /**
   * Add event listeners
   */
  addEventListeners() {
    if (this.elements.backButton) {
      this.addEventListener(this.elements.backButton, 'click', () => {
        // Close overlay
        const overlay = this.container.closest('#pokemon-caught-overlay');
        if (overlay) {
          overlay.style.display = 'none';
          overlay.dispatchEvent(new Event('closePokemonCaught'));
        }
      });
    }

    // Add event listeners for card flipping
    const teamCardContainers = this.container.querySelectorAll('.pokemon-team-section .pokemon-card-container');
    teamCardContainers.forEach(card => {
      this.addEventListener(card, 'click', (event) => {
        // Don't flip if clicking on a button
        if (event.target.closest('button')) return;

        // Toggle the flipped class
        card.classList.toggle('flipped');
      });
    });

    const caughtCardContainers = this.container.querySelectorAll('.pokemon-caught-section .pokemon-card-container');
    caughtCardContainers.forEach(card => {
      this.addEventListener(card, 'click', (event) => {
        // Don't flip if clicking on a button
        if (event.target.closest('button')) return;

        // Toggle the flipped class
        card.classList.toggle('flipped');
      });
    });

    // Add event listeners for "Add to Team" buttons
    const addToTeamButtons = this.container.querySelectorAll('.add-to-team-btn');
    addToTeamButtons.forEach(button => {
      this.addEventListener(button, 'click', async (event) => {
        event.stopPropagation(); // Prevent card flip

        const pokemonId = button.dataset.pokemonId;
        if (!pokemonId) return;

        // Find the Pokemon in the caught list
        const pokemon = this.caughtPokemon.find(p => p.id === pokemonId);
        if (!pokemon) {
          logger.warn(`Pokemon with ID ${pokemonId} not found in caught list`);
          return;
        }

        // Check if team is full
        if (this.teamPokemon.length >= 6) {
          alert('Dein Team ist bereits voll (max. 6 Pokémon)');
          return;
        }

        // Add to team
        const result = await addToTeam(pokemon);
        if (result.success) {
          // Reload team
          this.teamPokemon = await loadTeam();

          // Reload caught Pokemon from the Pokemon manager
          await pokemonManager.initialize();
          this.caughtPokemon = pokemonManager.getCaughtPokemon();
          logger.debug(`Reloaded ${this.caughtPokemon.length} caught Pokemon after team change`);

          // Re-render the screen
          this.render();
          this.addEventListeners();

          logger.debug(`Added ${pokemon.name} to team`);
        } else {
          alert(result.message || 'Fehler beim Hinzufügen zum Team');
        }
      });
    });

    // Add event listeners for "Remove from Team" buttons
    const removeFromTeamButtons = this.container.querySelectorAll('.remove-from-team-btn');
    removeFromTeamButtons.forEach(button => {
      this.addEventListener(button, 'click', async (event) => {
        event.stopPropagation(); // Prevent card flip

        const pokemonId = button.dataset.pokemonId;
        if (!pokemonId) return;

        // Find the Pokemon in the team
        const pokemon = this.teamPokemon.find(p => p.id === pokemonId);
        if (!pokemon) {
          logger.warn(`Pokemon with ID ${pokemonId} not found in team`);
          return;
        }

        // Remove from team
        const result = await removeFromTeam(pokemonId);
        if (result.success) {
          // Reload team
          this.teamPokemon = await loadTeam();

          // Reload caught Pokemon from the Pokemon manager
          await pokemonManager.initialize();
          this.caughtPokemon = pokemonManager.getCaughtPokemon();
          logger.debug(`Reloaded ${this.caughtPokemon.length} caught Pokemon after team change`);

          // Re-render the screen
          this.render();
          this.addEventListeners();

          logger.debug(`Removed ${pokemon.name} from team`);
        } else {
          alert(result.message || 'Fehler beim Entfernen aus dem Team');
        }
      });
    });

    // Add event listeners for "Make Buddy" buttons
    const makeBuddyButtons = this.container.querySelectorAll('.make-buddy-btn');
    logger.debug(`Found ${makeBuddyButtons.length} make-buddy buttons`);

    makeBuddyButtons.forEach(button => {
      // Log button details for debugging
      logger.debug(`Make buddy button: ${button.outerHTML}`);

      // Remove any existing event listeners to prevent duplicates
      const newButton = button.cloneNode(true);
      button.parentNode.replaceChild(newButton, button);

      // Add the event listener to the new button
      this.addEventListener(newButton, 'click', async (event) => {
        event.stopPropagation(); // Prevent card flip
        event.preventDefault(); // Prevent default button behavior

        logger.debug('Make buddy button clicked');

        const pokemonId = newButton.dataset.pokemonId;
        if (!pokemonId) {
          logger.warn('No Pokemon ID found in button data attribute');
          return;
        }

        logger.debug(`Attempting to make Pokemon with ID ${pokemonId} the buddy`);

        // Find the Pokemon in the team
        const pokemon = this.teamPokemon.find(p => p.id === pokemonId);
        if (!pokemon) {
          logger.warn(`Pokemon with ID ${pokemonId} not found in team`);
          return;
        }

        logger.debug(`Found Pokemon in team: ${pokemon.name}`);

        try {
          // Make this Pokemon the buddy (move to first position)
          const result = await makePokemonBuddy(pokemonId);
          logger.debug(`Make buddy result: ${JSON.stringify(result)}`);

          if (result.success) {
            // Reload team
            this.teamPokemon = await loadTeam();
            logger.debug(`Reloaded team with ${this.teamPokemon.length} Pokemon`);

            // Reload caught Pokemon from the Pokemon manager
            await pokemonManager.initialize();
            this.caughtPokemon = pokemonManager.getCaughtPokemon();
            logger.debug(`Reloaded ${this.caughtPokemon.length} caught Pokemon after team change`);

            // Re-render the screen
            this.render();
            this.addEventListeners();

            logger.debug(`${pokemon.name} is now the buddy Pokemon`);
          } else {
            logger.warn(`Failed to make ${pokemon.name} the buddy: ${result.message}`);
            alert(result.message || 'Fehler beim Festlegen als Buddy');
          }
        } catch (error) {
          logger.error(`Error making Pokemon buddy: ${error.message}`, error);
          alert('Ein Fehler ist aufgetreten. Bitte versuche es erneut.');
        }
      });
    });
    // HIER EINFÜGEN: Add event listeners for "Release Pokemon" buttons
const releasePokemonButtons = this.container.querySelectorAll('.release-pokemon-btn');
releasePokemonButtons.forEach(button => {
  this.addEventListener(button, 'click', async (event) => {
    event.stopPropagation(); // Prevent card flip

    const pokemonId = button.dataset.pokemonId;
    if (!pokemonId) return;

    // Find the Pokemon in the caught list
    const pokemon = this.caughtPokemon.find(p => p.id === pokemonId);
    if (!pokemon) {
      logger.warn(`Pokemon with ID ${pokemonId} not found in caught list`);
      return;
    }

    // Get German name for the confirmation dialog
    const displayName = getGermanPokemonName(pokemon);

    // Show confirmation dialog
    const confirmed = window.confirm(`Möchtest du ${displayName} wirklich frei lassen?`);
    
    if (confirmed) {
      try {
        // Remove Pokemon from storage using the Pokemon manager
        await pokemonManager.initialize();
        const success = await pokemonManager.removePokemon(pokemonId);

        if (success) {
          // Reload caught Pokemon from the Pokemon manager
          this.caughtPokemon = pokemonManager.getCaughtPokemon();
          logger.debug(`Released ${displayName} and reloaded ${this.caughtPokemon.length} caught Pokemon`);

          // Re-render the screen
          this.render();
          this.addEventListeners();

          logger.info(`Successfully released ${displayName} (ID: ${pokemonId})`);
        } else {
          alert('Fehler beim Freilassen des Pokémon. Bitte versuche es erneut.');
          logger.error(`Failed to release Pokemon ${displayName} (ID: ${pokemonId})`);
        }
      } catch (error) {
        logger.error(`Error releasing Pokemon ${displayName}:`, error);
        alert('Ein Fehler ist aufgetreten. Bitte versuche es erneut.');
      }
    }
  });
});
  }

  /**
   * Destroy the component and clean up all resources
   */
  destroy() {
    // Set destroyed flag to prevent async callbacks from executing
    this.isDestroyed = true;

    // Call parent destroy to clean up event listeners and container
    if (super.destroy) {
      super.destroy();
    }

    logger.debug('PokemonCaughtScreen destroyed');
  }
}

/**
 * Open the Pokemon caught screen
 */
export async function openPokemonCaughtScreen() {
  // Initialize the Pokemon manager to ensure it's ready
  pokemonManager.initialize().catch(e => {
    logger.error('Error initializing Pokemon manager:', e);
  });

  const { OverlayManager } = await import('../utils/overlay-manager.js');

  // Create the Pokemon caught screen
  const pokemonCaughtScreen = new PokemonCaughtScreen(null);

  const { overlay } = await OverlayManager.setupOverlay(
    'pokemon-caught-overlay',
    'pokemon-caught-overlay',
    'closePokemonCaught',
    null, // No additional close callback
    () => pokemonCaughtScreen.destroy() // Use cleanup callback for component cleanup
  );

  // Set the container after overlay is created
  pokemonCaughtScreen.container = overlay;
}

/**
 * Show the Pokemon caught screen with a specific Pokemon
 * @param {Object} pokemon - The Pokemon to display
 * @param {boolean} isEvolved - Whether this Pokemon has just evolved
 * @param {Function} callback - Optional callback function to call when screen is closed
 */
export async function showPokemonCaughtScreen(pokemon, isEvolved = false, callback = null) {
  // Initialize the Pokemon manager to ensure it's ready
  pokemonManager.initialize().catch(e => {
    logger.error('Error initializing Pokemon manager:', e);
  });

  const { OverlayManager } = await import('../utils/overlay-manager.js');

  // Create a custom caught screen for this specific Pokemon
  const pokemonCaughtScreen = new PokemonCaughtScreen(null, {
    caughtPokemon: [pokemon]
  });

  const { overlay } = await OverlayManager.setupOverlay(
    'pokemon-caught-overlay',
    'pokemon-caught-overlay',
    'closePokemonCaught',
    callback, // OverlayManager will handle the callback
    () => pokemonCaughtScreen.destroy() // Use cleanup callback for component cleanup
  );

  // Set the container after overlay is created
  pokemonCaughtScreen.container = overlay;

  // Add a special message if the Pokemon has evolved
  if (isEvolved) {
    const evolvedMessage = document.createElement('div');
    evolvedMessage.className = 'evolution-message';
    evolvedMessage.style.backgroundColor = 'rgba(255, 215, 0, 0.7)';
    evolvedMessage.style.color = '#333';
    evolvedMessage.style.padding = '10px';
    evolvedMessage.style.margin = '10px 0';
    evolvedMessage.style.borderRadius = '5px';
    evolvedMessage.style.textAlign = 'center';
    evolvedMessage.style.fontWeight = 'bold';
    evolvedMessage.innerHTML = `<span style="font-size: 1.2em;">✨ ${pokemon.name} ist gerade durch Evolution entstanden! ✨</span>`;

    // Insert the message after the header
    setTimeout(() => {
      const header = overlay.querySelector('.pokemon-caught-header');
      if (header && header.nextSibling) {
        header.parentNode.insertBefore(evolvedMessage, header.nextSibling);
      }
    }, 100);
  }
}
